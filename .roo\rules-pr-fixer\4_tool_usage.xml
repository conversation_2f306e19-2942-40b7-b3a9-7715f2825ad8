<tool_usage_guide>
  <tool_priorities>
    <priority level="1">
      <tool>gh pr view</tool>
      <when>Use at the start to get all review comments and PR metadata.</when>
      <why>Provides the core context of what needs to be fixed from a human perspective.</why>
    </priority>
    <priority level="2">
      <tool>gh pr checks</tool>
      <when>After getting comments, to check the technical status.</when>
      <why>Quickly identifies if there are failing automated checks that need investigation.</why>
   </priority>
   <priority level="3">
     <tool>new_task (mode: translate)</tool>
     <when>When changes affect user-facing content, i18n files, or UI components that require translation.</when>
     <why>Ensures translation consistency across all supported languages when PR fixes involve user-facing changes.</why>
   </priority>
   <priority level="4">
     <tool>gh pr checks --watch</tool>
     <when>After pushing a fix, to confirm that the changes have resolved the CI/CD failures.</when>
     <why>Provides real-time feedback on whether the fix was successful.</why>
   </priority>
 </tool_priorities>

  <tool_specific_guidance>
    <tool name="gh pr view">
      <best_practices>
        <practice>Always fetch details with --json to get structured data: gh pr view [PR_NUMBER] --repo [owner]/[repo] --json number,title,author,state,body,url,headRefName,baseRefName,files,additions,deletions,changedFiles,comments,reviews,mergeable,mergeStateStatus,isCrossRepository</practice>
        <practice>Parse the JSON output to extract branch name, owner, repo slug, and mergeable state.</practice>
      </best_practices>
    </tool>

    <tool name="gh pr comments">
      <best_practices>
        <practice>Use gh pr view --json comments to get all comments in structured format.</practice>
        <practice>Parse all comments to create a checklist of required changes.</practice>
        <practice>Ignore comments that are not actionable or have been resolved.</practice>
      </best_practices>
    </tool>

    <tool name="gh run view --log-failed">
      <best_practices>
        <practice>Use this command to get the exact error messages from failing tests.</practice>
        <practice>Search the log for keywords like 'error', 'failed', or 'exception' to quickly find the root cause.</practice>
        <practice>Always specify run ID explicitly to avoid interactive selection prompts: gh run view [RUN_ID] --log-failed</practice>
        <practice>Get run IDs with: gh run list --pr [PR_NUMBER] --repo [owner]/[repo]</practice>
      </best_practices>
    </tool>

    <tool name="gh pr checkout">
      <best_practices>
        <practice>Use --force flag: 'gh pr checkout [PR_NUMBER] --repo [owner]/[repo] --force'</practice>
        <practice>If gh checkout fails, use: git fetch origin pull/[PR_NUMBER]/head:[branch_name]</practice>
      </best_practices>
    </tool>

    <tool name="git operations">
      <best_practices>
        <practice>Use --force-with-lease for safer force pushing.</practice>
        <practice>Use GIT_EDITOR=true to prevent interactive prompts during rebases.</practice>
        <practice>Always determine the correct remote before pushing (origin vs fork).</practice>
      </best_practices>
      <remote_handling>
        <step>Check if PR is from a fork: 'gh pr view [PR_NUMBER] --repo [owner]/[repo] --json isCrossRepository'</step>
        <step>If isCrossRepository is true, add fork remote if needed</step>
        <step>Push to appropriate remote: 'git push --force-with-lease [remote] [branch]'</step>
      </remote_handling>
      <conflict_resolution>
        <step>Delegate to merge-resolver mode using new_task</step>
        <step>Provide the PR number (e.g., "#123") as the message</step>
        <step>The merge-resolver mode will handle all conflict resolution automatically</step>
      </conflict_resolution>
    </tool>

    <tool name="gh pr checks">
      <best_practices>
        <practice>Use --watch flag to monitor checks in real-time: 'gh pr checks [PR_NUMBER] --repo [owner]/[repo] --watch'</practice>
        <practice>For one-time status checks, use --json flag: 'gh pr checks [PR_NUMBER] --repo [owner]/[repo] --json state,conclusion,name'</practice>
        <practice>The --watch flag automatically updates the display as check statuses change.</practice>
        <practice>Use 'gh run list --pr [PR_NUMBER] --repo [owner]/[repo]' to get detailed workflow status if needed.</practice>
      </best_practices>
    </tool>
    
    <tool name="ask_followup_question">
      <best_practices>
        <practice>After analyzing all the problems (reviews, tests, conflicts), present a summary to the user.</practice>
        <practice>Provide clear, actionable next steps as suggestions.</practice>
        <practice>Example suggestions: "Address review comments first.", "Tackle the failing tests.", "Resolve merge conflicts."</practice>
      </best_practices>
    </tool>

    <tool name="new_task (mode: translate)">
      <best_practices>
        <practice>Use when PR fixes involve changes to user-facing strings, i18n files, or UI components.</practice>
        <practice>Provide specific details about what content needs translation in the message.</practice>
        <practice>Include file paths and descriptions of the changes made.</practice>
        <practice>List all affected languages that need updates.</practice>
        <practice>Wait for translation completion before proceeding to validation phase.</practice>
      </best_practices>
      <when_to_use>
        <trigger>Changes to webview-ui/src/i18n/locales/en/*.json files</trigger>
        <trigger>Changes to src/i18n/locales/en/*.json files</trigger>
        <trigger>Modifications to UI components with user-facing text</trigger>
        <trigger>Updates to announcement files or documentation requiring localization</trigger>
        <trigger>Addition of new error messages or user notifications</trigger>
      </when_to_use>
      <example_usage><![CDATA[
<new_task>
<mode>translate</mode>
<message>Translation updates needed for PR #1234 fixes. Please translate the following changes:

Files modified:
- webview-ui/src/i18n/locales/en/common.json: Added new error message "connection_failed"
- webview-ui/src/components/settings/ApiSettings.tsx: Updated button text from "Save" to "Save Configuration"

Please ensure all supported languages (ca, de, es, fr, hi, id, it, ja, ko, nl, pl, pt-BR, ru, tr, vi, zh-CN, zh-TW) are updated with appropriate translations for these changes.</message>
</new_task>
      ]]></example_usage>
    </tool>
    
    <tool name="new_task (mode: merge-resolver)">
      <best_practices>
        <practice>Use when PR has merge conflicts that need to be resolved.</practice>
        <practice>Simply provide the PR number (e.g., "#123") as the message.</practice>
        <practice>The merge-resolver mode will handle checkout, rebase, conflict resolution, and pushing.</practice>
        <practice>Wait for merge-resolver to complete before continuing with other PR fixes.</practice>
      </best_practices>
      <when_to_use>
        <trigger>When gh pr view shows mergeable: false or mergeStateStatus: CONFLICTING</trigger>
        <trigger>When git rebase fails with conflicts</trigger>
        <trigger>When git status shows unmerged paths</trigger>
      </when_to_use>
      <example_usage><![CDATA[
<new_task>
<mode>merge-resolver</mode>
<message>#1234</message>
</new_task>
      ]]></example_usage>
    </tool>
  </tool_specific_guidance>

  <github_cli_reference>
    <command_group name="pr_operations">
      <command>gh pr view [PR_NUMBER] --repo [owner]/[repo] --json [fields]</command>
      <command>gh pr checkout [PR_NUMBER] --repo [owner]/[repo] --force</command>
      <command>gh pr checks [PR_NUMBER] --repo [owner]/[repo] [--watch|--json]</command>
      <command>gh pr comment [PR_NUMBER] --repo [owner]/[repo] --body "[text]"</command>
    </command_group>

    <command_group name="workflow_operations">
      <command>gh run list --pr [PR_NUMBER] --repo [owner]/[repo]</command>
      <command>gh run view [RUN_ID] --repo [owner]/[repo] --log-failed</command>
      <command>gh workflow view [WORKFLOW_NAME] --repo [owner]/[repo]</command>
    </command_group>
  </github_cli_reference>
</tool_usage_guide>