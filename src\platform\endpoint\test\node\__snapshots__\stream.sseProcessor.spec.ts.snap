// Vitest Snapshot v1, https://vitest.dev/guide/snapshot.html

exports[`SSEProcessor > real world snapshots > multiple tool calls > completion results 1`] = `
"[
	{
		"solution": {
			"text": [
				"I",
				"'ll make two changes:",
				"\\n1. Ad",
				"d a multiply function to test",
				".js\\n2.",
				" Modify server.js",
				" to return a random",
				" dad joke from a",
				" small collection",
				" "
			],
			"newText": []
		},
		"reason": "tool_calls",
		"requestId": {
			"headerRequestId": "",
			"completionId": "ba49856c-7de2-485b-87cd-6ff61c58407e",
			"created": 1741911507,
			"serverExperiments": "",
			"deploymentId": ""
		},
		"index": 0,
		"usage": {
			"completion_tokens": 170,
			"prompt_tokens": 2128,
			"total_tokens": 2298
		}
	}
]"
`;

exports[`SSEProcessor > real world snapshots > multiple tool calls > finishedCallback chunks 1`] = `
"[
	{
		"text": "I",
		"index": 0,
		"delta": {
			"text": "I"
		}
	},
	{
		"text": "I'll make two changes:",
		"index": 0,
		"delta": {
			"text": "'ll make two changes:"
		}
	},
	{
		"text": "I'll make two changes:\\n1. Ad",
		"index": 0,
		"delta": {
			"text": "\\n1. Ad"
		}
	},
	{
		"text": "I'll make two changes:\\n1. Add a multiply function to test",
		"index": 0,
		"delta": {
			"text": "d a multiply function to test"
		}
	},
	{
		"text": "I'll make two changes:\\n1. Add a multiply function to test.js\\n2.",
		"index": 0,
		"delta": {
			"text": ".js\\n2."
		}
	},
	{
		"text": "I'll make two changes:\\n1. Add a multiply function to test.js\\n2. Modify server.js",
		"index": 0,
		"delta": {
			"text": " Modify server.js"
		}
	},
	{
		"text": "I'll make two changes:\\n1. Add a multiply function to test.js\\n2. Modify server.js to return a random",
		"index": 0,
		"delta": {
			"text": " to return a random"
		}
	},
	{
		"text": "I'll make two changes:\\n1. Add a multiply function to test.js\\n2. Modify server.js to return a random dad joke from a",
		"index": 0,
		"delta": {
			"text": " dad joke from a"
		}
	},
	{
		"text": "I'll make two changes:\\n1. Add a multiply function to test.js\\n2. Modify server.js to return a random dad joke from a small collection",
		"index": 0,
		"delta": {
			"text": " small collection"
		}
	},
	{
		"text": "I'll make two changes:\\n1. Add a multiply function to test.js\\n2. Modify server.js to return a random dad joke from a small collection ",
		"index": 0,
		"delta": {
			"text": " ",
			"beginToolCalls": [
				{
					"name": "edit_file"
				}
			]
		}
	},
	{
		"text": "I'll make two changes:\\n1. Add a multiply function to test.js\\n2. Modify server.js to return a random dad joke from a small collection ",
		"index": 0,
		"delta": {
			"text": "",
			"copilotToolCalls": [
				{
					"name": "edit_file",
					"arguments": "{\\"filePath\\":\\"/Users/<USER>/code/debugtest/test.js\\",\\"explanation\\":\\"Adding multiply function\\",\\"code\\":\\"// ...existing code...\\\\n\\nfunction multiply(a, b) {\\\\n    return a * b;\\\\\\"}",
					"id": "tooluse_448k6WHnTpS28K0Bd1bhgA"
				},
				{
					"name": "edit_file",
					"arguments": "\\"filePath\\":\\"/Users/<USER>/code/debugtest/server.js\\",\\"explanation\\":\\"Adding dad jokes functionality\\",\\"code\\": \\"const http = require('http');\\\\n\\\\nconst dadJokes = [\\\\n    'Why don\\\\\\\\'t eggs tell jokes? They\\\\'d crack up!',\\\\n    'What do you call a fake noodle? An impasta!',\\\\n    'Why did the scarecrow win an award? Because he was outstanding in his field!'\\\\n];\\\\n\\\\nconst server = http.createServer((req, res) => {\\\\n    console.log(\`\${req.method} \${req.url}\`);\\\\n    const randomJoke = dadJokes[Math.floor(Math.random() * dadJokes.length)];\\\\n    res.statusCode = 200;\\\\n    res.setHeader('Content-Type', 'text/plain');\\\\n    res.end(\`\${ randomJoke}\\\\\\n\`); \\\\n});\\\\n\\\\n// ...existing code...\\"}",
					"id": "tooluse_2SRF2HShTXOoLdGrjWuGiw"
				}
			],
			"thinking": {
				"text": "",
				"metadata": "tooluse_448k6WHnTpS28K0Bd1bhgA"
			}
		}
	}
]"
`;

exports[`SSEProcessor > real world snapshots > n > 1 - intent detector > completion results 1`] = `
"[
	{
		"solution": {
			"text": [
				"generate"
			],
			"newText": []
		},
		"reason": "stop",
		"requestId": {
			"headerRequestId": "",
			"completionId": "chatcmpl-9XTwlPjQvCcybmQxvS39Ouj4EEG89",
			"created": 1717766967,
			"serverExperiments": "",
			"deploymentId": ""
		},
		"index": 0
	},
	{
		"solution": {
			"text": [
				"generate"
			],
			"newText": []
		},
		"reason": "stop",
		"requestId": {
			"headerRequestId": "",
			"completionId": "chatcmpl-9XTwlPjQvCcybmQxvS39Ouj4EEG89",
			"created": 1717766967,
			"serverExperiments": "",
			"deploymentId": ""
		},
		"index": 1
	},
	{
		"solution": {
			"text": [
				"generate"
			],
			"newText": []
		},
		"reason": "stop",
		"requestId": {
			"headerRequestId": "",
			"completionId": "chatcmpl-9XTwlPjQvCcybmQxvS39Ouj4EEG89",
			"created": 1717766967,
			"serverExperiments": "",
			"deploymentId": ""
		},
		"index": 2
	}
]"
`;

exports[`SSEProcessor > real world snapshots > n > 1 - intent detector > finishedCallback chunks 1`] = `
"[
	{
		"text": "",
		"index": 0,
		"delta": {
			"text": ""
		}
	},
	{
		"text": "generate",
		"index": 0,
		"delta": {
			"text": "generate"
		}
	},
	{
		"text": "generate",
		"index": 0,
		"delta": {
			"text": ""
		}
	},
	{
		"text": "",
		"index": 1,
		"delta": {
			"text": ""
		}
	},
	{
		"text": "generate",
		"index": 1,
		"delta": {
			"text": "generate"
		}
	},
	{
		"text": "generate",
		"index": 1,
		"delta": {
			"text": ""
		}
	},
	{
		"text": "",
		"index": 2,
		"delta": {
			"text": ""
		}
	},
	{
		"text": "generate",
		"index": 2,
		"delta": {
			"text": "generate"
		}
	},
	{
		"text": "generate",
		"index": 2,
		"delta": {
			"text": ""
		}
	}
]"
`;

exports[`SSEProcessor > real world snapshots > panel chat - plain text assistant reply > completion results 1`] = `
"[
	{
		"solution": {
			"text": [
				"Hello",
				"!",
				" How",
				" can",
				" I",
				" assist",
				" you",
				" with",
				" your",
				" programming",
				" needs",
				" today",
				"?"
			],
			"newText": []
		},
		"reason": "stop",
		"requestId": {
			"headerRequestId": "",
			"completionId": "chatcmpl-9XTNuRVy7pKFgXL5VsZKe5wfpiWQU",
			"created": 1717764806,
			"serverExperiments": "",
			"deploymentId": ""
		},
		"index": 0
	}
]"
`;

exports[`SSEProcessor > real world snapshots > panel chat - plain text assistant reply > finishedCallback chunks 1`] = `
"[
	{
		"text": "",
		"index": 0,
		"delta": {
			"text": ""
		}
	},
	{
		"text": "Hello",
		"index": 0,
		"delta": {
			"text": "Hello"
		}
	},
	{
		"text": "Hello!",
		"index": 0,
		"delta": {
			"text": "!"
		}
	},
	{
		"text": "Hello! How",
		"index": 0,
		"delta": {
			"text": " How"
		}
	},
	{
		"text": "Hello! How can",
		"index": 0,
		"delta": {
			"text": " can"
		}
	},
	{
		"text": "Hello! How can I",
		"index": 0,
		"delta": {
			"text": " I"
		}
	},
	{
		"text": "Hello! How can I assist",
		"index": 0,
		"delta": {
			"text": " assist"
		}
	},
	{
		"text": "Hello! How can I assist you",
		"index": 0,
		"delta": {
			"text": " you"
		}
	},
	{
		"text": "Hello! How can I assist you with",
		"index": 0,
		"delta": {
			"text": " with"
		}
	},
	{
		"text": "Hello! How can I assist you with your",
		"index": 0,
		"delta": {
			"text": " your"
		}
	},
	{
		"text": "Hello! How can I assist you with your programming",
		"index": 0,
		"delta": {
			"text": " programming"
		}
	},
	{
		"text": "Hello! How can I assist you with your programming needs",
		"index": 0,
		"delta": {
			"text": " needs"
		}
	},
	{
		"text": "Hello! How can I assist you with your programming needs today",
		"index": 0,
		"delta": {
			"text": " today"
		}
	},
	{
		"text": "Hello! How can I assist you with your programming needs today?",
		"index": 0,
		"delta": {
			"text": "?"
		}
	},
	{
		"text": "Hello! How can I assist you with your programming needs today?",
		"index": 0,
		"delta": {
			"text": ""
		}
	}
]"
`;

exports[`SSEProcessor > real world snapshots > single function call > completion results 1`] = `
"[
	{
		"solution": {
			"text": [],
			"newText": []
		},
		"reason": "function_call",
		"requestId": {
			"headerRequestId": "",
			"completionId": "chatcmpl-9WkN0MexQZVb1yEgTn8jqMX24oHej",
			"created": 1717591770,
			"serverExperiments": "",
			"deploymentId": ""
		},
		"index": 0
	}
]"
`;

exports[`SSEProcessor > real world snapshots > single function call > finishedCallback chunks 1`] = `
"[
	{
		"text": "",
		"index": 0,
		"delta": {
			"text": "",
			"_deprecatedCopilotFunctionCalls": [
				{
					"name": "get_current_weather",
					"arguments": "{\\n  \\"location\\": \\"Z\\"\\n}"
				}
			]
		}
	}
]"
`;

exports[`SSEProcessor > real world snapshots > single tool call > completion results 1`] = `
"[
	{
		"solution": {
			"text": [],
			"newText": []
		},
		"reason": "tool_calls",
		"requestId": {
			"headerRequestId": "",
			"completionId": "chatcmpl-A5iQJExrRR9lZjObeHBzjesQ5HvT1",
			"created": 1725925767,
			"serverExperiments": "",
			"deploymentId": ""
		},
		"index": 0,
		"usage": {
			"completion_tokens": 0,
			"prompt_tokens": 218,
			"total_tokens": 218
		}
	}
]"
`;

exports[`SSEProcessor > real world snapshots > single tool call > finishedCallback chunks 1`] = `
"[
	{
		"text": "",
		"index": 0,
		"delta": {
			"text": "",
			"copilotToolCalls": [
				{
					"name": "copilot_searchCodebase",
					"arguments": "{\\"query\\":\\"linkedlist\\"}",
					"id": "call_ZQkSp6hzLvcGcH2L2CmaBkXM"
				}
			],
			"thinking": {
				"text": "",
				"metadata": "call_ZQkSp6hzLvcGcH2L2CmaBkXM"
			}
		}
	}
]"
`;

exports[`SSEProcessor > real world snapshots > single tool call with annotations attached > completion results 1`] = `
"[
	{
		"solution": {
			"text": [],
			"newText": []
		},
		"reason": "tool_calls",
		"requestId": {
			"headerRequestId": "",
			"completionId": "chatcmpl-A5iQJExrRR9lZjObeHBzjesQ5HvT1",
			"created": 1725925767,
			"serverExperiments": "",
			"deploymentId": ""
		},
		"index": 0,
		"usage": {
			"completion_tokens": 0,
			"prompt_tokens": 218,
			"total_tokens": 218
		}
	}
]"
`;

exports[`SSEProcessor > real world snapshots > single tool call with annotations attached > finishedCallback chunks 1`] = `
"[
	{
		"text": "",
		"index": 0,
		"delta": {
			"text": ""
		}
	},
	{
		"text": "",
		"index": 0,
		"delta": {
			"text": ""
		}
	},
	{
		"text": "",
		"index": 0,
		"delta": {
			"text": "",
			"copilotToolCalls": [
				{
					"name": "insert_edit_into_file",
					"arguments": "{\\"filePath\\":\\"/tmp/astro/src/pages/lists/[id]/index.astro\\",\\"code\\":\\"<UrlList />\\",\\"explanation\\":\\"Integrate the UrlList component into the list view page to display and manage URLs.\\"}",
					"id": "call_YEMsUraDh71gogljRLa7vyGP"
				}
			],
			"thinking": {
				"text": "",
				"metadata": "call_YEMsUraDh71gogljRLa7vyGP"
			}
		}
	}
]"
`;

exports[`SSEProcessor > real world snapshots > stream from @github (bing-search skill) > completion results 1`] = `
"[
	{
		"solution": {
			"text": [
				"The",
				" current",
				" LTS",
				" version",
				" of",
				" Node",
				".js",
				" is",
				" ",
				"20",
				".",
				"9",
				".",
				"0",
				",",
				" cod",
				"en",
				"amed",
				" '",
				"Iron",
				"'.",
				" This",
				" version",
				" transition",
				"ed",
				" into",
				" Long",
				" Term",
				" Support",
				" (",
				"L",
				"TS",
				")",
				" on",
				" ",
				"202",
				"3",
				"-",
				"10",
				"-",
				"24",
				" and",
				" will",
				" remain",
				" in",
				" \\"",
				"Active",
				" LTS",
				"\\"",
				" until",
				" October",
				" ",
				"202",
				"4",
				".",
				" After",
				" that",
				",",
				" it",
				" will",
				" move",
				" into",
				" \\"",
				"Maintenance",
				"\\"",
				" until",
				" its",
				" end",
				" of",
				" life",
				" in",
				" April",
				" ",
				"202",
				"6",
				".",
				" Here",
				" is",
				" the",
				" official",
				" [",
				"Node",
				".js",
				" release",
				" note",
				"](",
				"https",
				"://",
				"node",
				"js",
				".org",
				"/en",
				"/blog",
				"/release",
				"/v",
				"20",
				".",
				"9",
				".",
				"0",
				")."
			],
			"newText": []
		},
		"reason": "stop",
		"requestId": {
			"headerRequestId": "",
			"completionId": "chatcmpl-9X3aOY9DtjweQLHsmnLgzQYg36KpW",
			"created": 1717665636,
			"serverExperiments": "",
			"deploymentId": ""
		},
		"index": 0
	},
	{
		"solution": {
			"text": [
				"The",
				" current",
				" LTS",
				" version",
				" of",
				" Node",
				".js",
				" is",
				" ",
				"20",
				".",
				"9",
				".",
				"0",
				",",
				" cod",
				"en",
				"amed",
				" '",
				"Iron",
				"'.",
				" This",
				" version",
				" transition",
				"ed",
				" into",
				" Long",
				" Term",
				" Support",
				" (",
				"L",
				"TS",
				")",
				" on",
				" ",
				"202",
				"3",
				"-",
				"10",
				"-",
				"24",
				" and",
				" will",
				" remain",
				" in",
				" \\"",
				"Active",
				" LTS",
				"\\"",
				" until",
				" October",
				" ",
				"202",
				"4",
				".",
				" After",
				" that",
				",",
				" it",
				" will",
				" move",
				" into",
				" \\"",
				"Maintenance",
				"\\"",
				" until",
				" its",
				" end",
				" of",
				" life",
				" in",
				" April",
				" ",
				"202",
				"6",
				".",
				" Here",
				" is",
				" the",
				" official",
				" [",
				"Node",
				".js",
				" release",
				" note",
				"](",
				"https",
				"://",
				"node",
				"js",
				".org",
				"/en",
				"/blog",
				"/release",
				"/v",
				"20",
				".",
				"9",
				".",
				"0",
				")."
			],
			"newText": []
		},
		"reason": "function_call",
		"requestId": {
			"headerRequestId": "",
			"completionId": "chatcmpl-9X3aOY9DtjweQLHsmnLgzQYg36KpW",
			"created": 1717665636,
			"serverExperiments": "",
			"deploymentId": ""
		},
		"index": 0
	}
]"
`;

exports[`SSEProcessor > real world snapshots > stream from @github (bing-search skill) > finishedCallback chunks 1`] = `
"[
	{
		"text": "",
		"index": 0,
		"delta": {
			"text": "",
			"_deprecatedCopilotFunctionCalls": [
				{
					"name": "bing-search",
					"arguments": "{\\n  \\"query\\": \\"current LTS version of Node.js}"
				}
			]
		}
	},
	{
		"text": "",
		"index": 0,
		"delta": {
			"text": ""
		}
	},
	{
		"text": "The",
		"index": 0,
		"delta": {
			"text": "The"
		}
	},
	{
		"text": "The current",
		"index": 0,
		"delta": {
			"text": " current"
		}
	},
	{
		"text": "The current LTS",
		"index": 0,
		"delta": {
			"text": " LTS"
		}
	},
	{
		"text": "The current LTS version",
		"index": 0,
		"delta": {
			"text": " version"
		}
	},
	{
		"text": "The current LTS version of",
		"index": 0,
		"delta": {
			"text": " of"
		}
	},
	{
		"text": "The current LTS version of Node",
		"index": 0,
		"delta": {
			"text": " Node"
		}
	},
	{
		"text": "The current LTS version of Node.js",
		"index": 0,
		"delta": {
			"text": ".js"
		}
	},
	{
		"text": "The current LTS version of Node.js is",
		"index": 0,
		"delta": {
			"text": " is"
		}
	},
	{
		"text": "The current LTS version of Node.js is ",
		"index": 0,
		"delta": {
			"text": " "
		}
	},
	{
		"text": "The current LTS version of Node.js is 20",
		"index": 0,
		"delta": {
			"text": "20"
		}
	},
	{
		"text": "The current LTS version of Node.js is 20.",
		"index": 0,
		"delta": {
			"text": "."
		}
	},
	{
		"text": "The current LTS version of Node.js is 20.9",
		"index": 0,
		"delta": {
			"text": "9"
		}
	},
	{
		"text": "The current LTS version of Node.js is 20.9.",
		"index": 0,
		"delta": {
			"text": "."
		}
	},
	{
		"text": "The current LTS version of Node.js is 20.9.0",
		"index": 0,
		"delta": {
			"text": "0"
		}
	},
	{
		"text": "The current LTS version of Node.js is 20.9.0,",
		"index": 0,
		"delta": {
			"text": ","
		}
	},
	{
		"text": "The current LTS version of Node.js is 20.9.0, cod",
		"index": 0,
		"delta": {
			"text": " cod"
		}
	},
	{
		"text": "The current LTS version of Node.js is 20.9.0, coden",
		"index": 0,
		"delta": {
			"text": "en"
		}
	},
	{
		"text": "The current LTS version of Node.js is 20.9.0, codenamed",
		"index": 0,
		"delta": {
			"text": "amed"
		}
	},
	{
		"text": "The current LTS version of Node.js is 20.9.0, codenamed '",
		"index": 0,
		"delta": {
			"text": " '"
		}
	},
	{
		"text": "The current LTS version of Node.js is 20.9.0, codenamed 'Iron",
		"index": 0,
		"delta": {
			"text": "Iron"
		}
	},
	{
		"text": "The current LTS version of Node.js is 20.9.0, codenamed 'Iron'.",
		"index": 0,
		"delta": {
			"text": "'."
		}
	},
	{
		"text": "The current LTS version of Node.js is 20.9.0, codenamed 'Iron'. This",
		"index": 0,
		"delta": {
			"text": " This"
		}
	},
	{
		"text": "The current LTS version of Node.js is 20.9.0, codenamed 'Iron'. This version",
		"index": 0,
		"delta": {
			"text": " version"
		}
	},
	{
		"text": "The current LTS version of Node.js is 20.9.0, codenamed 'Iron'. This version transition",
		"index": 0,
		"delta": {
			"text": " transition"
		}
	},
	{
		"text": "The current LTS version of Node.js is 20.9.0, codenamed 'Iron'. This version transitioned",
		"index": 0,
		"delta": {
			"text": "ed"
		}
	},
	{
		"text": "The current LTS version of Node.js is 20.9.0, codenamed 'Iron'. This version transitioned into",
		"index": 0,
		"delta": {
			"text": " into"
		}
	},
	{
		"text": "The current LTS version of Node.js is 20.9.0, codenamed 'Iron'. This version transitioned into Long",
		"index": 0,
		"delta": {
			"text": " Long"
		}
	},
	{
		"text": "The current LTS version of Node.js is 20.9.0, codenamed 'Iron'. This version transitioned into Long Term",
		"index": 0,
		"delta": {
			"text": " Term"
		}
	},
	{
		"text": "The current LTS version of Node.js is 20.9.0, codenamed 'Iron'. This version transitioned into Long Term Support",
		"index": 0,
		"delta": {
			"text": " Support"
		}
	},
	{
		"text": "The current LTS version of Node.js is 20.9.0, codenamed 'Iron'. This version transitioned into Long Term Support (",
		"index": 0,
		"delta": {
			"text": " ("
		}
	},
	{
		"text": "The current LTS version of Node.js is 20.9.0, codenamed 'Iron'. This version transitioned into Long Term Support (L",
		"index": 0,
		"delta": {
			"text": "L"
		}
	},
	{
		"text": "The current LTS version of Node.js is 20.9.0, codenamed 'Iron'. This version transitioned into Long Term Support (LTS",
		"index": 0,
		"delta": {
			"text": "TS"
		}
	},
	{
		"text": "The current LTS version of Node.js is 20.9.0, codenamed 'Iron'. This version transitioned into Long Term Support (LTS)",
		"index": 0,
		"delta": {
			"text": ")"
		}
	},
	{
		"text": "The current LTS version of Node.js is 20.9.0, codenamed 'Iron'. This version transitioned into Long Term Support (LTS) on",
		"index": 0,
		"delta": {
			"text": " on"
		}
	},
	{
		"text": "The current LTS version of Node.js is 20.9.0, codenamed 'Iron'. This version transitioned into Long Term Support (LTS) on ",
		"index": 0,
		"delta": {
			"text": " "
		}
	},
	{
		"text": "The current LTS version of Node.js is 20.9.0, codenamed 'Iron'. This version transitioned into Long Term Support (LTS) on 202",
		"index": 0,
		"delta": {
			"text": "202"
		}
	},
	{
		"text": "The current LTS version of Node.js is 20.9.0, codenamed 'Iron'. This version transitioned into Long Term Support (LTS) on 2023",
		"index": 0,
		"delta": {
			"text": "3"
		}
	},
	{
		"text": "The current LTS version of Node.js is 20.9.0, codenamed 'Iron'. This version transitioned into Long Term Support (LTS) on 2023-",
		"index": 0,
		"delta": {
			"text": "-"
		}
	},
	{
		"text": "The current LTS version of Node.js is 20.9.0, codenamed 'Iron'. This version transitioned into Long Term Support (LTS) on 2023-10",
		"index": 0,
		"delta": {
			"text": "10"
		}
	},
	{
		"text": "The current LTS version of Node.js is 20.9.0, codenamed 'Iron'. This version transitioned into Long Term Support (LTS) on 2023-10-",
		"index": 0,
		"delta": {
			"text": "-"
		}
	},
	{
		"text": "The current LTS version of Node.js is 20.9.0, codenamed 'Iron'. This version transitioned into Long Term Support (LTS) on 2023-10-24",
		"index": 0,
		"delta": {
			"text": "24"
		}
	},
	{
		"text": "The current LTS version of Node.js is 20.9.0, codenamed 'Iron'. This version transitioned into Long Term Support (LTS) on 2023-10-24 and",
		"index": 0,
		"delta": {
			"text": " and"
		}
	},
	{
		"text": "The current LTS version of Node.js is 20.9.0, codenamed 'Iron'. This version transitioned into Long Term Support (LTS) on 2023-10-24 and will",
		"index": 0,
		"delta": {
			"text": " will"
		}
	},
	{
		"text": "The current LTS version of Node.js is 20.9.0, codenamed 'Iron'. This version transitioned into Long Term Support (LTS) on 2023-10-24 and will remain",
		"index": 0,
		"delta": {
			"text": " remain"
		}
	},
	{
		"text": "The current LTS version of Node.js is 20.9.0, codenamed 'Iron'. This version transitioned into Long Term Support (LTS) on 2023-10-24 and will remain in",
		"index": 0,
		"delta": {
			"text": " in"
		}
	},
	{
		"text": "The current LTS version of Node.js is 20.9.0, codenamed 'Iron'. This version transitioned into Long Term Support (LTS) on 2023-10-24 and will remain in \\"",
		"index": 0,
		"delta": {
			"text": " \\""
		}
	},
	{
		"text": "The current LTS version of Node.js is 20.9.0, codenamed 'Iron'. This version transitioned into Long Term Support (LTS) on 2023-10-24 and will remain in \\"Active",
		"index": 0,
		"delta": {
			"text": "Active"
		}
	},
	{
		"text": "The current LTS version of Node.js is 20.9.0, codenamed 'Iron'. This version transitioned into Long Term Support (LTS) on 2023-10-24 and will remain in \\"Active LTS",
		"index": 0,
		"delta": {
			"text": " LTS"
		}
	},
	{
		"text": "The current LTS version of Node.js is 20.9.0, codenamed 'Iron'. This version transitioned into Long Term Support (LTS) on 2023-10-24 and will remain in \\"Active LTS\\"",
		"index": 0,
		"delta": {
			"text": "\\""
		}
	},
	{
		"text": "The current LTS version of Node.js is 20.9.0, codenamed 'Iron'. This version transitioned into Long Term Support (LTS) on 2023-10-24 and will remain in \\"Active LTS\\" until",
		"index": 0,
		"delta": {
			"text": " until"
		}
	},
	{
		"text": "The current LTS version of Node.js is 20.9.0, codenamed 'Iron'. This version transitioned into Long Term Support (LTS) on 2023-10-24 and will remain in \\"Active LTS\\" until October",
		"index": 0,
		"delta": {
			"text": " October"
		}
	},
	{
		"text": "The current LTS version of Node.js is 20.9.0, codenamed 'Iron'. This version transitioned into Long Term Support (LTS) on 2023-10-24 and will remain in \\"Active LTS\\" until October ",
		"index": 0,
		"delta": {
			"text": " "
		}
	},
	{
		"text": "The current LTS version of Node.js is 20.9.0, codenamed 'Iron'. This version transitioned into Long Term Support (LTS) on 2023-10-24 and will remain in \\"Active LTS\\" until October 202",
		"index": 0,
		"delta": {
			"text": "202"
		}
	},
	{
		"text": "The current LTS version of Node.js is 20.9.0, codenamed 'Iron'. This version transitioned into Long Term Support (LTS) on 2023-10-24 and will remain in \\"Active LTS\\" until October 2024",
		"index": 0,
		"delta": {
			"text": "4"
		}
	},
	{
		"text": "The current LTS version of Node.js is 20.9.0, codenamed 'Iron'. This version transitioned into Long Term Support (LTS) on 2023-10-24 and will remain in \\"Active LTS\\" until October 2024.",
		"index": 0,
		"delta": {
			"text": "."
		}
	},
	{
		"text": "The current LTS version of Node.js is 20.9.0, codenamed 'Iron'. This version transitioned into Long Term Support (LTS) on 2023-10-24 and will remain in \\"Active LTS\\" until October 2024. After",
		"index": 0,
		"delta": {
			"text": " After"
		}
	},
	{
		"text": "The current LTS version of Node.js is 20.9.0, codenamed 'Iron'. This version transitioned into Long Term Support (LTS) on 2023-10-24 and will remain in \\"Active LTS\\" until October 2024. After that",
		"index": 0,
		"delta": {
			"text": " that"
		}
	},
	{
		"text": "The current LTS version of Node.js is 20.9.0, codenamed 'Iron'. This version transitioned into Long Term Support (LTS) on 2023-10-24 and will remain in \\"Active LTS\\" until October 2024. After that,",
		"index": 0,
		"delta": {
			"text": ","
		}
	},
	{
		"text": "The current LTS version of Node.js is 20.9.0, codenamed 'Iron'. This version transitioned into Long Term Support (LTS) on 2023-10-24 and will remain in \\"Active LTS\\" until October 2024. After that, it",
		"index": 0,
		"delta": {
			"text": " it"
		}
	},
	{
		"text": "The current LTS version of Node.js is 20.9.0, codenamed 'Iron'. This version transitioned into Long Term Support (LTS) on 2023-10-24 and will remain in \\"Active LTS\\" until October 2024. After that, it will",
		"index": 0,
		"delta": {
			"text": " will"
		}
	},
	{
		"text": "The current LTS version of Node.js is 20.9.0, codenamed 'Iron'. This version transitioned into Long Term Support (LTS) on 2023-10-24 and will remain in \\"Active LTS\\" until October 2024. After that, it will move",
		"index": 0,
		"delta": {
			"text": " move"
		}
	},
	{
		"text": "The current LTS version of Node.js is 20.9.0, codenamed 'Iron'. This version transitioned into Long Term Support (LTS) on 2023-10-24 and will remain in \\"Active LTS\\" until October 2024. After that, it will move into",
		"index": 0,
		"delta": {
			"text": " into"
		}
	},
	{
		"text": "The current LTS version of Node.js is 20.9.0, codenamed 'Iron'. This version transitioned into Long Term Support (LTS) on 2023-10-24 and will remain in \\"Active LTS\\" until October 2024. After that, it will move into \\"",
		"index": 0,
		"delta": {
			"text": " \\""
		}
	},
	{
		"text": "The current LTS version of Node.js is 20.9.0, codenamed 'Iron'. This version transitioned into Long Term Support (LTS) on 2023-10-24 and will remain in \\"Active LTS\\" until October 2024. After that, it will move into \\"Maintenance",
		"index": 0,
		"delta": {
			"text": "Maintenance"
		}
	},
	{
		"text": "The current LTS version of Node.js is 20.9.0, codenamed 'Iron'. This version transitioned into Long Term Support (LTS) on 2023-10-24 and will remain in \\"Active LTS\\" until October 2024. After that, it will move into \\"Maintenance\\"",
		"index": 0,
		"delta": {
			"text": "\\""
		}
	},
	{
		"text": "The current LTS version of Node.js is 20.9.0, codenamed 'Iron'. This version transitioned into Long Term Support (LTS) on 2023-10-24 and will remain in \\"Active LTS\\" until October 2024. After that, it will move into \\"Maintenance\\" until",
		"index": 0,
		"delta": {
			"text": " until"
		}
	},
	{
		"text": "The current LTS version of Node.js is 20.9.0, codenamed 'Iron'. This version transitioned into Long Term Support (LTS) on 2023-10-24 and will remain in \\"Active LTS\\" until October 2024. After that, it will move into \\"Maintenance\\" until its",
		"index": 0,
		"delta": {
			"text": " its"
		}
	},
	{
		"text": "The current LTS version of Node.js is 20.9.0, codenamed 'Iron'. This version transitioned into Long Term Support (LTS) on 2023-10-24 and will remain in \\"Active LTS\\" until October 2024. After that, it will move into \\"Maintenance\\" until its end",
		"index": 0,
		"delta": {
			"text": " end"
		}
	},
	{
		"text": "The current LTS version of Node.js is 20.9.0, codenamed 'Iron'. This version transitioned into Long Term Support (LTS) on 2023-10-24 and will remain in \\"Active LTS\\" until October 2024. After that, it will move into \\"Maintenance\\" until its end of",
		"index": 0,
		"delta": {
			"text": " of"
		}
	},
	{
		"text": "The current LTS version of Node.js is 20.9.0, codenamed 'Iron'. This version transitioned into Long Term Support (LTS) on 2023-10-24 and will remain in \\"Active LTS\\" until October 2024. After that, it will move into \\"Maintenance\\" until its end of life",
		"index": 0,
		"delta": {
			"text": " life"
		}
	},
	{
		"text": "The current LTS version of Node.js is 20.9.0, codenamed 'Iron'. This version transitioned into Long Term Support (LTS) on 2023-10-24 and will remain in \\"Active LTS\\" until October 2024. After that, it will move into \\"Maintenance\\" until its end of life in",
		"index": 0,
		"delta": {
			"text": " in"
		}
	},
	{
		"text": "The current LTS version of Node.js is 20.9.0, codenamed 'Iron'. This version transitioned into Long Term Support (LTS) on 2023-10-24 and will remain in \\"Active LTS\\" until October 2024. After that, it will move into \\"Maintenance\\" until its end of life in April",
		"index": 0,
		"delta": {
			"text": " April"
		}
	},
	{
		"text": "The current LTS version of Node.js is 20.9.0, codenamed 'Iron'. This version transitioned into Long Term Support (LTS) on 2023-10-24 and will remain in \\"Active LTS\\" until October 2024. After that, it will move into \\"Maintenance\\" until its end of life in April ",
		"index": 0,
		"delta": {
			"text": " "
		}
	},
	{
		"text": "The current LTS version of Node.js is 20.9.0, codenamed 'Iron'. This version transitioned into Long Term Support (LTS) on 2023-10-24 and will remain in \\"Active LTS\\" until October 2024. After that, it will move into \\"Maintenance\\" until its end of life in April 202",
		"index": 0,
		"delta": {
			"text": "202"
		}
	},
	{
		"text": "The current LTS version of Node.js is 20.9.0, codenamed 'Iron'. This version transitioned into Long Term Support (LTS) on 2023-10-24 and will remain in \\"Active LTS\\" until October 2024. After that, it will move into \\"Maintenance\\" until its end of life in April 2026",
		"index": 0,
		"delta": {
			"text": "6"
		}
	},
	{
		"text": "The current LTS version of Node.js is 20.9.0, codenamed 'Iron'. This version transitioned into Long Term Support (LTS) on 2023-10-24 and will remain in \\"Active LTS\\" until October 2024. After that, it will move into \\"Maintenance\\" until its end of life in April 2026.",
		"index": 0,
		"delta": {
			"text": "."
		}
	},
	{
		"text": "The current LTS version of Node.js is 20.9.0, codenamed 'Iron'. This version transitioned into Long Term Support (LTS) on 2023-10-24 and will remain in \\"Active LTS\\" until October 2024. After that, it will move into \\"Maintenance\\" until its end of life in April 2026. Here",
		"index": 0,
		"delta": {
			"text": " Here"
		}
	},
	{
		"text": "The current LTS version of Node.js is 20.9.0, codenamed 'Iron'. This version transitioned into Long Term Support (LTS) on 2023-10-24 and will remain in \\"Active LTS\\" until October 2024. After that, it will move into \\"Maintenance\\" until its end of life in April 2026. Here is",
		"index": 0,
		"delta": {
			"text": " is"
		}
	},
	{
		"text": "The current LTS version of Node.js is 20.9.0, codenamed 'Iron'. This version transitioned into Long Term Support (LTS) on 2023-10-24 and will remain in \\"Active LTS\\" until October 2024. After that, it will move into \\"Maintenance\\" until its end of life in April 2026. Here is the",
		"index": 0,
		"delta": {
			"text": " the"
		}
	},
	{
		"text": "The current LTS version of Node.js is 20.9.0, codenamed 'Iron'. This version transitioned into Long Term Support (LTS) on 2023-10-24 and will remain in \\"Active LTS\\" until October 2024. After that, it will move into \\"Maintenance\\" until its end of life in April 2026. Here is the official",
		"index": 0,
		"delta": {
			"text": " official"
		}
	},
	{
		"text": "The current LTS version of Node.js is 20.9.0, codenamed 'Iron'. This version transitioned into Long Term Support (LTS) on 2023-10-24 and will remain in \\"Active LTS\\" until October 2024. After that, it will move into \\"Maintenance\\" until its end of life in April 2026. Here is the official [",
		"index": 0,
		"delta": {
			"text": " ["
		}
	},
	{
		"text": "The current LTS version of Node.js is 20.9.0, codenamed 'Iron'. This version transitioned into Long Term Support (LTS) on 2023-10-24 and will remain in \\"Active LTS\\" until October 2024. After that, it will move into \\"Maintenance\\" until its end of life in April 2026. Here is the official [Node",
		"index": 0,
		"delta": {
			"text": "Node"
		}
	},
	{
		"text": "The current LTS version of Node.js is 20.9.0, codenamed 'Iron'. This version transitioned into Long Term Support (LTS) on 2023-10-24 and will remain in \\"Active LTS\\" until October 2024. After that, it will move into \\"Maintenance\\" until its end of life in April 2026. Here is the official [Node.js",
		"index": 0,
		"delta": {
			"text": ".js"
		}
	},
	{
		"text": "The current LTS version of Node.js is 20.9.0, codenamed 'Iron'. This version transitioned into Long Term Support (LTS) on 2023-10-24 and will remain in \\"Active LTS\\" until October 2024. After that, it will move into \\"Maintenance\\" until its end of life in April 2026. Here is the official [Node.js release",
		"index": 0,
		"delta": {
			"text": " release"
		}
	},
	{
		"text": "The current LTS version of Node.js is 20.9.0, codenamed 'Iron'. This version transitioned into Long Term Support (LTS) on 2023-10-24 and will remain in \\"Active LTS\\" until October 2024. After that, it will move into \\"Maintenance\\" until its end of life in April 2026. Here is the official [Node.js release note",
		"index": 0,
		"delta": {
			"text": " note"
		}
	},
	{
		"text": "The current LTS version of Node.js is 20.9.0, codenamed 'Iron'. This version transitioned into Long Term Support (LTS) on 2023-10-24 and will remain in \\"Active LTS\\" until October 2024. After that, it will move into \\"Maintenance\\" until its end of life in April 2026. Here is the official [Node.js release note](",
		"index": 0,
		"delta": {
			"text": "]("
		}
	},
	{
		"text": "The current LTS version of Node.js is 20.9.0, codenamed 'Iron'. This version transitioned into Long Term Support (LTS) on 2023-10-24 and will remain in \\"Active LTS\\" until October 2024. After that, it will move into \\"Maintenance\\" until its end of life in April 2026. Here is the official [Node.js release note](https",
		"index": 0,
		"delta": {
			"text": "https"
		}
	},
	{
		"text": "The current LTS version of Node.js is 20.9.0, codenamed 'Iron'. This version transitioned into Long Term Support (LTS) on 2023-10-24 and will remain in \\"Active LTS\\" until October 2024. After that, it will move into \\"Maintenance\\" until its end of life in April 2026. Here is the official [Node.js release note](https://",
		"index": 0,
		"delta": {
			"text": "://"
		}
	},
	{
		"text": "The current LTS version of Node.js is 20.9.0, codenamed 'Iron'. This version transitioned into Long Term Support (LTS) on 2023-10-24 and will remain in \\"Active LTS\\" until October 2024. After that, it will move into \\"Maintenance\\" until its end of life in April 2026. Here is the official [Node.js release note](https://node",
		"index": 0,
		"delta": {
			"text": "node"
		}
	},
	{
		"text": "The current LTS version of Node.js is 20.9.0, codenamed 'Iron'. This version transitioned into Long Term Support (LTS) on 2023-10-24 and will remain in \\"Active LTS\\" until October 2024. After that, it will move into \\"Maintenance\\" until its end of life in April 2026. Here is the official [Node.js release note](https://nodejs",
		"index": 0,
		"delta": {
			"text": "js"
		}
	},
	{
		"text": "The current LTS version of Node.js is 20.9.0, codenamed 'Iron'. This version transitioned into Long Term Support (LTS) on 2023-10-24 and will remain in \\"Active LTS\\" until October 2024. After that, it will move into \\"Maintenance\\" until its end of life in April 2026. Here is the official [Node.js release note](https://nodejs.org",
		"index": 0,
		"delta": {
			"text": ".org"
		}
	},
	{
		"text": "The current LTS version of Node.js is 20.9.0, codenamed 'Iron'. This version transitioned into Long Term Support (LTS) on 2023-10-24 and will remain in \\"Active LTS\\" until October 2024. After that, it will move into \\"Maintenance\\" until its end of life in April 2026. Here is the official [Node.js release note](https://nodejs.org/en",
		"index": 0,
		"delta": {
			"text": "/en"
		}
	},
	{
		"text": "The current LTS version of Node.js is 20.9.0, codenamed 'Iron'. This version transitioned into Long Term Support (LTS) on 2023-10-24 and will remain in \\"Active LTS\\" until October 2024. After that, it will move into \\"Maintenance\\" until its end of life in April 2026. Here is the official [Node.js release note](https://nodejs.org/en/blog",
		"index": 0,
		"delta": {
			"text": "/blog"
		}
	},
	{
		"text": "The current LTS version of Node.js is 20.9.0, codenamed 'Iron'. This version transitioned into Long Term Support (LTS) on 2023-10-24 and will remain in \\"Active LTS\\" until October 2024. After that, it will move into \\"Maintenance\\" until its end of life in April 2026. Here is the official [Node.js release note](https://nodejs.org/en/blog/release",
		"index": 0,
		"delta": {
			"text": "/release"
		}
	},
	{
		"text": "The current LTS version of Node.js is 20.9.0, codenamed 'Iron'. This version transitioned into Long Term Support (LTS) on 2023-10-24 and will remain in \\"Active LTS\\" until October 2024. After that, it will move into \\"Maintenance\\" until its end of life in April 2026. Here is the official [Node.js release note](https://nodejs.org/en/blog/release/v",
		"index": 0,
		"delta": {
			"text": "/v"
		}
	},
	{
		"text": "The current LTS version of Node.js is 20.9.0, codenamed 'Iron'. This version transitioned into Long Term Support (LTS) on 2023-10-24 and will remain in \\"Active LTS\\" until October 2024. After that, it will move into \\"Maintenance\\" until its end of life in April 2026. Here is the official [Node.js release note](https://nodejs.org/en/blog/release/v20",
		"index": 0,
		"delta": {
			"text": "20"
		}
	},
	{
		"text": "The current LTS version of Node.js is 20.9.0, codenamed 'Iron'. This version transitioned into Long Term Support (LTS) on 2023-10-24 and will remain in \\"Active LTS\\" until October 2024. After that, it will move into \\"Maintenance\\" until its end of life in April 2026. Here is the official [Node.js release note](https://nodejs.org/en/blog/release/v20.",
		"index": 0,
		"delta": {
			"text": "."
		}
	},
	{
		"text": "The current LTS version of Node.js is 20.9.0, codenamed 'Iron'. This version transitioned into Long Term Support (LTS) on 2023-10-24 and will remain in \\"Active LTS\\" until October 2024. After that, it will move into \\"Maintenance\\" until its end of life in April 2026. Here is the official [Node.js release note](https://nodejs.org/en/blog/release/v20.9",
		"index": 0,
		"delta": {
			"text": "9"
		}
	},
	{
		"text": "The current LTS version of Node.js is 20.9.0, codenamed 'Iron'. This version transitioned into Long Term Support (LTS) on 2023-10-24 and will remain in \\"Active LTS\\" until October 2024. After that, it will move into \\"Maintenance\\" until its end of life in April 2026. Here is the official [Node.js release note](https://nodejs.org/en/blog/release/v20.9.",
		"index": 0,
		"delta": {
			"text": "."
		}
	},
	{
		"text": "The current LTS version of Node.js is 20.9.0, codenamed 'Iron'. This version transitioned into Long Term Support (LTS) on 2023-10-24 and will remain in \\"Active LTS\\" until October 2024. After that, it will move into \\"Maintenance\\" until its end of life in April 2026. Here is the official [Node.js release note](https://nodejs.org/en/blog/release/v20.9.0",
		"index": 0,
		"delta": {
			"text": "0"
		}
	},
	{
		"text": "The current LTS version of Node.js is 20.9.0, codenamed 'Iron'. This version transitioned into Long Term Support (LTS) on 2023-10-24 and will remain in \\"Active LTS\\" until October 2024. After that, it will move into \\"Maintenance\\" until its end of life in April 2026. Here is the official [Node.js release note](https://nodejs.org/en/blog/release/v20.9.0).",
		"index": 0,
		"delta": {
			"text": ")."
		}
	},
	{
		"text": "The current LTS version of Node.js is 20.9.0, codenamed 'Iron'. This version transitioned into Long Term Support (LTS) on 2023-10-24 and will remain in \\"Active LTS\\" until October 2024. After that, it will move into \\"Maintenance\\" until its end of life in April 2026. Here is the official [Node.js release note](https://nodejs.org/en/blog/release/v20.9.0).",
		"index": 0,
		"delta": {
			"text": ""
		}
	}
]"
`;
