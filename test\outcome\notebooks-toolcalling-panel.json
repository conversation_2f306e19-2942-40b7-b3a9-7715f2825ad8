[{"name": "notebooks (toolCalling) [panel] - Edit cell tool", "requests": ["9834c0b5f7a58f5e79cd64abe699f4d7186221cf9d281fe69e57666ea5ad7c15"]}, {"name": "notebooks (toolCalling) [panel] - New Notebook Tool with EditFile and EditNotebook", "requests": ["f90d7fcc60499f21c4979e3d3c0e603ab40fc2f3ddbe174e500972d80d0c40e1"]}, {"name": "notebooks (toolCalling) [panel] - New Notebook Tool without EditFile and with EditNotebook", "requests": ["f90d7fcc60499f21c4979e3d3c0e603ab40fc2f3ddbe174e500972d80d0c40e1"]}, {"name": "notebooks (toolCalling) [panel] - New Notebook Tool without EditFile and without EditNotebook", "requests": ["f90d7fcc60499f21c4979e3d3c0e603ab40fc2f3ddbe174e500972d80d0c40e1"]}, {"name": "notebooks (toolCalling) [panel] - Run cell at a specific index", "requests": ["de558cbf574dfc710aafbdba47162f4917c3d1d4ac488239615ec46b9ea62d29"]}, {"name": "notebooks (toolCalling) [panel] - Run cell tool", "requests": ["f6efefd1b8e139f5a7da827446818addef21b9d2e4c0ce34a61c4bcbc026f7d4"]}, {"name": "notebooks (toolCalling) [panel] - Run cell tool should avoid running markdown cells", "requests": ["e17ee6c17a85e6c0419e38830b7cbc247410d2f31a495726c140999e8d073441"]}]