<workflow>
  <initialization>
    <step number="1">
      <name>Initialize Issue Creation Process</name>
      <instructions>
        When the user requests to create an issue, immediately set up a todo list to track the workflow.
        
        <update_todo_list>
        <todos>
        [ ] Analyze user request to determine issue type
        [ ] Gather initial information for the issue
        [ ] Determine if user wants to contribute
        [ ] Perform technical analysis (if contributing)
        [ ] Draft issue content
        [ ] Review and confirm with user
        [ ] Create GitHub issue
        </todos>
        </update_todo_list>
      </instructions>
    </step>
  </initialization>

  <step number="1">
    <name>Determine Issue Type</name>
    <instructions>
      Analyze the user's initial request to automatically assess whether they're reporting a bug or proposing a feature.
      Look for keywords and context clues:
      
      Bug indicators:
      - Words like "error", "broken", "not working", "fails", "crash", "bug"
      - Descriptions of unexpected behavior
      - Error messages or stack traces
      - References to something that used to work
      
      Feature indicators:
      - Words like "feature", "enhancement", "add", "implement", "would be nice"
      - Descriptions of new functionality
      - Suggestions for improvements
      - "It would be great if..."
      
      Based on your analysis, order the options with the most likely choice first:
      
      <ask_followup_question>
      <question>Based on your request, what type of issue would you like to create?</question>
      <follow_up>
      [If bug indicators found:]
      <suggest>Bug Report - Report a problem with existing functionality</suggest>
      <suggest>Detailed Feature Proposal - Propose a new feature or enhancement</suggest>
      
      [If feature indicators found:]
      <suggest>Detailed Feature Proposal - Propose a new feature or enhancement</suggest>
      <suggest>Bug Report - Report a problem with existing functionality</suggest>
      
      [If unclear:]
      <suggest>Bug Report - Report a problem with existing functionality</suggest>
      <suggest>Detailed Feature Proposal - Propose a new feature or enhancement</suggest>
      </follow_up>
      </ask_followup_question>
      
      After determining the type, update the todo list:
      <update_todo_list>
      <todos>
      [x] Analyze user request to determine issue type
      [-] Gather initial information for the issue
      [ ] Determine if user wants to contribute
      [ ] Perform technical analysis (if contributing)
      [ ] Draft issue content
      [ ] Review and confirm with user
      [ ] Create GitHub issue
      </todos>
      </update_todo_list>
    </instructions>
  </step>

  <step number="2">
    <name>Gather Initial Information</name>
    <instructions>
      Based on the user's initial prompt or request, extract key information.
      If the user hasn't provided enough detail, use ask_followup_question to gather
      the required fields from the appropriate template.
      
      For Bug Reports, ensure you have:
      - App version (ask user to check in VSCode extension panel if unknown)
      - API provider being used
      - Model being used
      - Clear steps to reproduce
      - What happened vs what was expected
      - Any error messages or logs
      
      For Feature Requests, ensure you have:
      - Specific problem description with impact (who is affected, when it happens, current vs expected behavior, impact)
      - Additional context if available (mockups, screenshots, links)
      
      IMPORTANT: Do NOT ask for solution design, acceptance criteria, or technical details 
      unless the user explicitly states they want to contribute the implementation.
      
      Use multiple ask_followup_question calls if needed to gather all information.
      Be specific in your questions based on what's missing.
      
      After gathering information, update the todo:
      <update_todo_list>
      <todos>
      [x] Analyze user request to determine issue type
      [x] Gather initial information for the issue
      [-] Determine if user wants to contribute
      [ ] Perform technical analysis (if contributing)
      [ ] Draft issue content
      [ ] Review and confirm with user
      [ ] Create GitHub issue
      </todos>
      </update_todo_list>
    </instructions>
  </step>

  <step number="3">
    <name>Determine if User Wants to Contribute</name>
    <instructions>
      Before exploring the codebase, determine if the user wants to contribute the implementation:
      
      <ask_followup_question>
      <question>Are you interested in implementing this yourself, or are you just reporting the problem for the Roo team to solve?</question>
      <follow_up>
      <suggest>Just reporting the problem - the Roo team can design the solution</suggest>
      <suggest>I want to contribute and implement this myself</suggest>
      <suggest>I'm not sure yet, but I'd like to provide technical analysis</suggest>
      </follow_up>
      </ask_followup_question>
      
      Based on their response:
      - If just reporting: Skip to step 5 (Draft Issue - Problem Only)
      - If contributing: Continue to step 4 (Technical Analysis)
      - If providing analysis: Continue to step 4 but make technical sections optional
      
      Update the todo based on the decision:
      <update_todo_list>
      <todos>
      [x] Analyze user request to determine issue type
      [x] Gather initial information for the issue
      [x] Determine if user wants to contribute
      [If contributing: [ ] Perform technical analysis (if contributing)]
      [If not contributing: [-] Perform technical analysis (skipped - not contributing)]
      [-] Draft issue content
      [ ] Review and confirm with user
      [ ] Create GitHub issue
      </todos>
      </update_todo_list>
    </instructions>
  </step>

  <step number="4">
    <name>Technical Analysis for Contributors</name>
    <instructions>
      ONLY perform this step if the user wants to contribute or provide technical analysis.
      
      This step uses the comprehensive technical analysis sub-workflow defined in
      6_technical_analysis_workflow.xml. The sub-workflow will:
      
      1. Create its own detailed investigation todo list
      2. Perform exhaustive codebase searches using iterative refinement
      3. Analyze all relevant files and dependencies
      4. Form and validate hypotheses about the implementation
      5. Create a comprehensive technical solution
      6. Define detailed acceptance criteria
      
      To execute the technical analysis sub-workflow:
      - Follow all phases defined in 6_technical_analysis_workflow.xml
      - Use the aggressive investigation approach from issue-investigator mode
      - Document all findings in extreme detail
      - Ensure the analysis is thorough enough for automated implementation
      
      The sub-workflow will manage its own todo list for the investigation process
      and will produce a comprehensive technical analysis section for the issue.
      
      After completing the technical analysis:
      <update_todo_list>
      <todos>
      [x] Analyze user request to determine issue type
      [x] Gather initial information for the issue
      [x] Determine if user wants to contribute
      [x] Perform technical analysis (if contributing)
      [-] Draft issue content
      [ ] Review and confirm with user
      [ ] Create GitHub issue
      </todos>
      </update_todo_list>
    </instructions>
  </step>

  <step number="5">
    <name>Draft Issue Content</name>
    <instructions>
      Create the issue body based on whether the user is just reporting or contributing.
      
      For Bug Reports, format is the same regardless of contribution intent:
      ```
      ## App Version
      [version from user]
      
      ## API Provider
      [provider from dropdown list]
      
      ## Model Used
      [exact model name]
      
      ## 🔁 Steps to Reproduce
      
      1. [First step with specific details]
      2. [Second step with exact actions]
      3. [Continue numbering all steps]
      
      Include:
      - Exact button clicks or menu selections
      - Specific input text or prompts used
      - File names and paths involved
      - Any settings or configuration
      
      ## 💥 Outcome Summary
      
      Expected: [what should have happened]
      Actual: [what actually happened]
      
      ## 📄 Relevant Logs or Errors
      
      ```[language]
      [paste any error messages or logs]
      ```
      
      [If user is contributing, add the comprehensive technical analysis section from step 4]
      ```
      
      For Feature Requests - PROBLEM REPORTERS (not contributing):
      ```
      ## What specific problem does this solve?
      
      [Detailed problem description following the template guidelines]
      
      **Who is affected:** [user groups]
      **When this happens:** [specific scenarios]
      **Current behavior:** [what happens now]
      **Expected behavior:** [what should happen]
      **Impact:** [time wasted, errors, productivity loss]
      
      ## Additional context
      
      [Any mockups, screenshots, links, or other supporting information]
      ```
      
      For Feature Requests - CONTRIBUTORS (implementing the feature):
      ```
      ## What specific problem does this solve?
      
      [Detailed problem description following the template guidelines]
      
      **Who is affected:** [user groups]
      **When this happens:** [specific scenarios]
      **Current behavior:** [what happens now]
      **Expected behavior:** [what should happen]
      **Impact:** [time wasted, errors, productivity loss]
      
      ## Additional context
      
      [Any mockups, screenshots, links, or other supporting information]
      
      ---
      
      ## 🛠️ Contributing & Technical Analysis
      
      ✅ **I'm interested in implementing this feature**
      ✅ **I understand this needs approval before implementation begins**
      
      [Insert the comprehensive technical analysis section from step 4, including:]
      - Root cause / Implementation target
      - Affected components with file paths and line numbers
      - Current implementation analysis
      - Detailed proposed implementation steps
      - Code architecture considerations
      - Testing requirements
      - Performance impact
      - Security considerations
      - Migration strategy
      - Rollback plan
      - Dependencies and breaking changes
      - Implementation complexity assessment
      
      ## Acceptance Criteria
      
      [Insert the detailed acceptance criteria from the technical analysis]
      ```
      
      After drafting:
      <update_todo_list>
      <todos>
      [x] Analyze user request to determine issue type
      [x] Gather initial information for the issue
      [x] Determine if user wants to contribute
      [x] Perform technical analysis (if contributing)
      [x] Draft issue content
      [-] Review and confirm with user
      [ ] Create GitHub issue
      </todos>
      </update_todo_list>
    </instructions>
  </step>

  <step number="6">
    <name>Review and Confirm with User</name>
    <instructions>
      Present the complete drafted issue to the user for review:
      
      <ask_followup_question>
      <question>I've prepared the following GitHub issue. Please review it carefully:

      [Show the complete formatted issue content]

      Would you like me to create this issue, or would you like to make any changes?</question>
      <follow_up>
      <suggest>Yes, create this issue in RooCodeInc/Roo-Code</suggest>
      <suggest>Modify the problem description</suggest>
      <suggest>Add more technical details</suggest>
      <suggest>Change the title to: [let me specify]</suggest>
      </follow_up>
      </ask_followup_question>
      
      If user requests changes, make them and show the updated version for confirmation.
      
      After confirmation:
      <update_todo_list>
      <todos>
      [x] Analyze user request to determine issue type
      [x] Gather initial information for the issue
      [x] Determine if user wants to contribute
      [x] Perform technical analysis (if contributing)
      [x] Draft issue content
      [x] Review and confirm with user
      [-] Create GitHub issue
      </todos>
      </update_todo_list>
    </instructions>
  </step>

  <step number="7">
    <name>Create GitHub Issue</name>
    <instructions>
      Once user confirms, create the issue using the GitHub CLI:
      
      First, save the issue body to a temporary file:
      <execute_command>
      <command>cat > /tmp/issue_body.md << 'EOF'
[The complete formatted issue body from step 5]
EOF</command>
      </execute_command>
      
      Then create the issue:
      <execute_command>
      <command>gh issue create --repo RooCodeInc/Roo-Code --title "[Create a descriptive title based on the issue content]" --body-file /tmp/issue_body.md --label "bug"</command>
      </execute_command>
      
      For feature requests, use labels "proposal,enhancement":
      <execute_command>
      <command>gh issue create --repo RooCodeInc/Roo-Code --title "[Create a descriptive title based on the issue content]" --body-file /tmp/issue_body.md --label "proposal" --label "enhancement"</command>
      </execute_command>
      
      The command will return the issue URL. Inform the user of the created issue number and URL.
      
      Clean up the temporary file:
      <execute_command>
      <command>rm /tmp/issue_body.md</command>
      </execute_command>
      
      Complete the workflow:
      <update_todo_list>
      <todos>
      [x] Analyze user request to determine issue type
      [x] Gather initial information for the issue
      [x] Determine if user wants to contribute
      [x] Perform technical analysis (if contributing)
      [x] Draft issue content
      [x] Review and confirm with user
      [x] Create GitHub issue
      </todos>
      </update_todo_list>
    </instructions>
  </step>
</workflow>